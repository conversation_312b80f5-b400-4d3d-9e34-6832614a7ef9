#!/usr/bin/env python3
"""
Google Contacts CSV Cleanup Script
This script cleans up the Google Contacts CSV file by:
1. Removing invalid/system entries
2. Standardizing phone number formats
3. Fixing malformed entries
4. Removing duplicates
5. Standardizing data formatting
"""

import csv
import re
import sys
from collections import defaultdict

def normalize_phone(phone):
    """Normalize phone number format"""
    if not phone:
        return ""

    # Handle multiple numbers separated by ::: or other separators
    if ':::' in phone:
        # Take the first number
        phone = phone.split(':::')[0].strip()

    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone)

    # Skip if too short or obviously invalid
    if len(cleaned) < 7:
        return ""

    # Skip obviously invalid numbers (like repeated digits)
    if len(set(cleaned.replace('+', ''))) <= 2:
        return ""

    # Handle different formats
    if cleaned.startswith('+1'):
        # US number with country code
        digits = cleaned[2:]
        if len(digits) == 10:
            return f"+1 ({digits[:3]}) {digits[3:6]}-{digits[6:]}"
    elif cleaned.startswith('1') and len(cleaned) == 11:
        # US number starting with 1
        digits = cleaned[1:]
        if len(digits) == 10:
            return f"+1 ({digits[:3]}) {digits[3:6]}-{digits[6:]}"
    elif len(cleaned) == 10:
        # 10-digit US number
        return f"+1 ({cleaned[:3]}) {cleaned[3:6]}-{cleaned[6:]}"
    elif len(cleaned) == 7:
        # 7-digit local number
        return f"{cleaned[:3]}-{cleaned[3:]}"

    # For international or other formats, return cleaned version
    if len(cleaned) > 10 and cleaned.startswith('+'):
        return cleaned
    elif len(cleaned) > 10:
        return f"+{cleaned}"

    return phone  # Return original if can't normalize

def is_valid_contact(row):
    """Check if a contact entry is valid"""
    first_name = row[0].strip()
    last_name = row[2].strip()
    phone1 = row[26].strip()
    email1 = row[18].strip()
    
    # Skip system entries
    system_entries = [
        'Contacts+', 'Corporate', 'getAbstract', 'hrdirectselfservice', 
        'Instapaper', 'Evernote Account'
    ]
    
    if first_name in system_entries:
        return False
    
    # Skip service numbers
    if first_name in ['411', '611', 'Customer'] and 'Care' in last_name:
        return False
    
    # Skip entries that are just numbers or very short
    if first_name.isdigit() or (len(first_name) <= 2 and not last_name and not email1):
        return False
    
    # Skip entries with no meaningful data
    if not first_name and not last_name and not phone1 and not email1:
        return False
    
    # Skip obvious test/placeholder entries
    if 'test' in first_name.lower() or first_name.startswith('('):
        return False
    
    return True

def clean_name(name):
    """Clean and standardize name formatting"""
    if not name:
        return ""
    
    # Remove extra spaces and capitalize properly
    name = ' '.join(name.split())
    
    # Handle special cases
    if name.lower() == 'unknown contact':
        return name
    
    # Capitalize first letter of each word, but preserve existing capitalization for abbreviations
    words = name.split()
    cleaned_words = []
    for word in words:
        if len(word) <= 3 and word.isupper():
            # Keep short uppercase words (like initials)
            cleaned_words.append(word)
        else:
            cleaned_words.append(word.capitalize())
    
    return ' '.join(cleaned_words)

def clean_notes(notes):
    """Clean up notes field"""
    if not notes:
        return ""
    
    # Remove system messages and clean up formatting
    notes = notes.replace('Imported on 5/7/24 ::: ', '')
    notes = notes.replace('* myContacts', '').strip()
    
    # Remove multiple colons and clean up
    notes = re.sub(r':::', ' | ', notes)
    notes = re.sub(r'\s+', ' ', notes)
    
    return notes.strip()

def main():
    input_file = 'Google Contacts.csv'
    output_file = 'Google Contacts_CLEANED.csv'
    
    print("Starting Google Contacts cleanup...")
    
    valid_contacts = []
    skipped_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8', newline='') as infile:
            reader = csv.reader(infile)
            header = next(reader)  # Read header
            
            for row_num, row in enumerate(reader, start=2):
                # Ensure row has enough columns
                while len(row) < len(header):
                    row.append('')
                
                if is_valid_contact(row):
                    # Clean up the contact data
                    cleaned_row = row.copy()
                    
                    # Clean names
                    cleaned_row[0] = clean_name(row[0])  # First Name
                    cleaned_row[1] = clean_name(row[1])  # Middle Name  
                    cleaned_row[2] = clean_name(row[2])  # Last Name
                    cleaned_row[8] = clean_name(row[8])  # Nickname
                    
                    # Clean phone numbers
                    cleaned_row[26] = normalize_phone(row[26])  # Phone 1
                    cleaned_row[28] = normalize_phone(row[28])  # Phone 2
                    cleaned_row[30] = normalize_phone(row[30])  # Phone 3
                    
                    # Clean notes
                    cleaned_row[14] = clean_notes(row[14])
                    
                    # Clean organization
                    cleaned_row[10] = clean_name(row[10])  # Organization Name
                    
                    valid_contacts.append(cleaned_row)
                else:
                    skipped_count += 1
                    print(f"Skipped invalid contact on line {row_num}: {row[0][:30]}...")
        
        # Write cleaned contacts
        with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(header)  # Write header
            writer.writerows(valid_contacts)
        
        print(f"Cleanup complete!")
        print(f"Valid contacts: {len(valid_contacts)}")
        print(f"Skipped contacts: {skipped_count}")
        print(f"Cleaned file saved as: {output_file}")
        
    except Exception as e:
        print(f"Error during cleanup: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
